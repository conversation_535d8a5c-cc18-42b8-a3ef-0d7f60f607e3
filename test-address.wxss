/* 测试页面样式 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.section {
  background-color: white;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-left: 6rpx solid #007aff;
  padding-left: 15rpx;
}

.address-info {
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 20rpx;
}

.info-row {
  display: flex;
  margin-bottom: 15rpx;
  align-items: flex-start;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  width: 120rpx;
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
}

.value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
}

.value.success {
  color: #28a745;
  font-weight: bold;
}

.value.error {
  color: #dc3545;
  font-weight: bold;
}

.actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 8rpx;
  font-size: 30rpx;
  border: none;
}

.btn.primary {
  background-color: #007aff;
  color: white;
}

.btn.secondary {
  background-color: #6c757d;
  color: white;
}

.test-results {
  max-height: 600rpx;
  overflow-y: auto;
}

.test-item {
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
}

.test-item.valid {
  border-color: #28a745;
  background-color: #f8fff9;
}

.test-item.invalid {
  border-color: #dc3545;
  background-color: #fff8f8;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.timestamp {
  font-size: 24rpx;
  color: #666;
}

.status {
  font-size: 26rpx;
  font-weight: bold;
}

.test-item.valid .status {
  color: #28a745;
}

.test-item.invalid .status {
  color: #dc3545;
}

.test-details {
  display: flex;
  gap: 30rpx;
  font-size: 24rpx;
  color: #666;
}
