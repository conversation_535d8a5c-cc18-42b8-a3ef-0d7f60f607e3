<!-- 测试地址管理组件的经纬度功能 -->
<view class="container">
  <view class="header">
    <text class="title">地址管理组件测试</text>
    <text class="subtitle">验证地址选择时是否正确返回经纬度信息</text>
  </view>

  <!-- 地址选择组件 -->
  <view class="section">
    <text class="section-title">地址选择</text>
    <address-manager
      selected-address="{{selectedAddress}}"
      placeholder="请选择配送地址"
      bind:addresschange="onAddressChange">
    </address-manager>
  </view>

  <!-- 当前选中地址信息 -->
  <view class="section" wx:if="{{selectedAddress}}">
    <text class="section-title">当前选中地址</text>
    <view class="address-info">
      <view class="info-row">
        <text class="label">姓名:</text>
        <text class="value">{{selectedAddress.name}}</text>
      </view>
      <view class="info-row">
        <text class="label">电话:</text>
        <text class="value">{{selectedAddress.phone}}</text>
      </view>
      <view class="info-row">
        <text class="label">地址:</text>
        <text class="value">{{selectedAddress.province}} {{selectedAddress.city}} {{selectedAddress.district}} {{selectedAddress.detail}}</text>
      </view>
      <view class="info-row">
        <text class="label">纬度:</text>
        <text class="value {{selectedAddress.latitude ? 'success' : 'error'}}">
          {{selectedAddress.latitude || '未设置'}}
        </text>
      </view>
      <view class="info-row">
        <text class="label">经度:</text>
        <text class="value {{selectedAddress.longitude ? 'success' : 'error'}}">
          {{selectedAddress.longitude || '未设置'}}
        </text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="actions">
    <button class="btn primary" bindtap="simulateOrderCreation">模拟订单创建</button>
    <button class="btn secondary" bindtap="clearTestResults">清除测试结果</button>
  </view>

  <!-- 测试结果 -->
  <view class="section" wx:if="{{testResults.length > 0}}">
    <text class="section-title">测试结果</text>
    <view class="test-results">
      <view class="test-item {{item.isValid ? 'valid' : 'invalid'}}" wx:for="{{testResults}}" wx:key="timestamp">
        <view class="test-header">
          <text class="timestamp">{{item.timestamp}}</text>
          <text class="status">{{item.isValid ? '✓ 有效' : '✗ 无效'}}</text>
        </view>
        <view class="test-details">
          <text>包含纬度: {{item.hasLatitude ? '是' : '否'}}</text>
          <text>包含经度: {{item.hasLongitude ? '是' : '否'}}</text>
        </view>
      </view>
    </view>
  </view>
</view>
