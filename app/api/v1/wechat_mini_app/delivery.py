# -*- coding: utf-8 -*-
# 外卖订单模块

import time
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Header
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.service.wechat_miniapp.wx_user import WeChatUserService
from app.dao.account import account_dao
from app.service.order import order_service
from app.service.fengniao import FengniaoClient
from app.utils.logger import logger
from app.models.order import OrderType
from app.core.config import settings

router = APIRouter()


@router.post("/delivery-order/create")
async def create_delivery_order(
        task_info: dict,
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """创建外卖订单
    
    Args:
        task_info: 订单信息，包含：
            - order_type: 订单类型，固定为"delivery"
            - items: 商品列表，每个商品包含dish_id、name、price、quantity
            - total_amount: 订单总金额
            - delivery_address: 配送地址信息
            - delivery_time: 配送时间信息
            - delivery_fee: 配送费
            - coupon_id: 优惠券ID字符串，格式如"14,17"（可选）
            - coupon_discount: 预期优惠金额（可选）
        token: 用户token
        db: 数据库会话
    """
    logger.info(f"创建外卖订单，接收到的参数: {task_info}")
    
    try:
        # 验证用户token
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.warning(f"token无效或已过期: {token[:10] if token else 'None'}...")
            return {
                "code": 401,
                "message": "未登录",
                "data": None
            }
        
        user_id = user.id
        logger.info(f"用户token验证成功，用户ID: {user_id}")
        
        # 提取优惠券参数
        coupon_id_str = task_info.get("coupon_id", "")
        coupon_discount = task_info.get("coupon_discount", 0)
        
        # 解析优惠券ID列表
        coupon_usage_record_ids = []
        if coupon_id_str and coupon_id_str.strip():
            try:
                coupon_usage_record_ids = [int(id.strip()) for id in coupon_id_str.split(",") if id.strip()]
                logger.info(f"解析优惠券ID列表: {coupon_usage_record_ids}")
            except ValueError as e:
                logger.error(f"优惠券ID格式错误: {coupon_id_str}, 错误: {str(e)}")
                return {
                    "code": 400,
                    "message": "优惠券ID格式错误",
                    "data": None
                }
        
        logger.info(f"优惠券参数 - coupon_usage_record_ids: {coupon_usage_record_ids}, coupon_discount: {coupon_discount}")
        
        # 验证必填参数
        required_fields = ['order_type', 'items', 'total_amount', 'delivery_address']
        
        for field in required_fields:
            if field not in task_info or task_info[field] is None:
                logger.error(f"参数错误，缺少必填字段: {field}")
                return {
                    "code": 400,
                    "message": f"缺少必填参数: {field}",
                    "data": None
                }
        
        # 验证订单类型
        if task_info['order_type'] != 'delivery':
            logger.error(f"订单类型错误: {task_info['order_type']}")
            return {
                "code": 400,
                "message": "订单类型必须为delivery",
                "data": None
            }
        
        # 验证商品列表
        items = task_info['items']
        if not items or not isinstance(items, list):
            logger.error("商品列表为空或格式错误")
            return {
                "code": 400,
                "message": "商品列表不能为空",
                "data": None
            }
        
        # 验证配送地址
        delivery_address = task_info['delivery_address']
        if not isinstance(delivery_address, dict):
            logger.error("配送地址格式错误")
            return {
                "code": 400,
                "message": "配送地址格式错误",
                "data": None
            }
        
        # 验证配送地址必填字段
        address_required_fields = ['name', 'phone', 'detail']
        for field in address_required_fields:
            if field not in delivery_address or not delivery_address[field]:
                logger.error(f"配送地址缺少必填字段: {field}")
                return {
                    "code": 400,
                    "message": f"配送地址缺少必填字段: {field}",
                    "data": None
                }
        
        # 构建产品列表
        products = []
        for item in items:
            # 验证商品必填字段
            item_required_fields = ['dish_id', 'quantity']
            for field in item_required_fields:
                if field not in item or item[field] is None:
                    logger.error(f"商品参数错误，缺少必填字段: {field}")
                    return {
                        "code": 400,
                        "message": f"商品缺少必填参数: {field}",
                        "data": None
                    }
            
            # 验证数量为正整数
            try:
                quantity = int(item['quantity'])
                if quantity <= 0:
                    raise ValueError("数量必须为正整数")
            except (ValueError, TypeError):
                logger.error(f"商品数量格式错误: {item.get('quantity')}")
                return {
                    "code": 400,
                    "message": "商品数量必须为正整数",
                    "data": None
                }
            
            products.append({
                "product_id": int(item["dish_id"]),
                "quantity": quantity
            })
        
        logger.info(f"创建商品列表: {products}")
        
        # 创建本地订单
        order = order_service.create_order(
            db, 
            user_id, 
            products,
            rule_data=None,  # 外卖订单不需要规则数据
            coupon_usage_record_ids=coupon_usage_record_ids,
            order_type=OrderType.DELIVERY
        )
        
        order_id = order.id
        order_no = order.order_no
        order_payable_amount = order.payable_amount
        
        logger.info(f"创建本地订单: {order}")
        logger.info(f"创建的订单号: {order_no}")
        logger.info(f"创建的订单金额: {order_payable_amount}")
        logger.info(f"订单优惠金额: {order.total_discount_amount}")
        
        # 验证优惠金额
        if coupon_usage_record_ids and coupon_discount > 0:
            actual_discount = order.total_discount_amount or 0.0
            if abs(actual_discount - coupon_discount) > 0.01:  # 允许0.01的误差
                logger.error(f"优惠金额不匹配 - 预期: {coupon_discount}, 实际: {actual_discount}")
                db.rollback()
                return {
                    "code": 400,
                    "message": f"优惠金额验证失败，预期优惠{coupon_discount}元，实际优惠{actual_discount}元",
                    "data": None
                }
            logger.info(f"优惠金额验证通过 - 预期: {coupon_discount}, 实际: {actual_discount}")
        
        # 准备蜂鸟下单数据
        fengniao_order_data = _prepare_fengniao_order_data(task_info, order, delivery_address)
        
        # 调用蜂鸟API创建订单
        fengniao_client = FengniaoClient()
        fengniao_result = fengniao_client.create_order(fengniao_order_data)
        
        logger.info(f"蜂鸟API响应: {fengniao_result}")
        
        # 检查蜂鸟API调用结果
        if fengniao_result.get("code") != "200":
            logger.error(f"蜂鸟下单失败: {fengniao_result.get('msg', '未知错误')}")
            # 回滚本地订单
            db.rollback()
            return {
                "code": 500,
                "message": f"外卖下单失败: {fengniao_result.get('msg', '未知错误')}",
                "data": None
            }
        
        # 解析蜂鸟订单号
        fengniao_order_id = None
        business_data = fengniao_result.get("business_data")
        if business_data:
            import json
            if isinstance(business_data, str):
                business_data = json.loads(business_data)
            fengniao_order_id = business_data.get("order_id")
        
        # 更新本地订单的蜂鸟信息
        if fengniao_order_id:
            # 格式化配送地址
            formatted_address = _format_delivery_address(delivery_address)

            # 格式化配送时间
            formatted_time = _format_delivery_time(task_info.get("delivery_time"))

            # 这里需要更新DeliveryOrder的fengniao_order_id字段
            # 由于当前的order_service.create_order返回的是基础Order对象
            # 我们需要通过DAO直接更新
            from app.dao.order import order_dao
            update_data = {
                "fengniao_order_id": str(fengniao_order_id),
                "delivery_address_raw": delivery_address,
                "delivery_address": formatted_address,
                "delivery_time_raw": task_info.get("delivery_time"),
                "delivery_time": formatted_time,
                "delivery_fee": task_info.get("delivery_fee", 0)
            }
            order_dao.update(db, order_id, update_data)
            logger.info(f"更新订单蜂鸟信息成功，蜂鸟订单号: {fengniao_order_id}")
        
        # 获取用户余额
        user_balance = account_dao.get_user_balance(db, user_id)
        logger.info(f"用户余额: {user_balance}")
        
        return {
            "code": 200,
            "message": "success",
            "data": {
                'order_no': order_no,
                'order_id': order_id,
                'payable_amount': order_payable_amount,
                'user_balance': user_balance,
                'fengniao_order_id': fengniao_order_id
            }
        }
        
    except Exception as e:
        logger.error(f"创建外卖订单失败: {str(e)}", exc_info=True)
        db.rollback()
        return {
            "code": 500,
            "message": f"创建订单失败: {str(e)}",
            "data": None
        }


def _prepare_fengniao_order_data(task_info: dict, order, delivery_address: dict) -> dict:
    """准备蜂鸟下单数据"""
    
    # 获取当前时间戳（毫秒）
    current_timestamp = int(time.time() * 1000)
    
    # 构建商品列表
    goods_item_list = []
    for item in task_info['items']:
        goods_item_list.append({
            "item_id": str(item['dish_id']),
            "item_name": item.get('name', '商品'),
            "item_quantity": int(item['quantity']),
            "item_amount_cent": int(float(item.get('price', 0)) * 100),  # 转换为分
            "item_actual_amount_cent": int(float(item.get('price', 0)) * int(item['quantity']) * 100)  # 转换为分
        })
    
    # 构建蜂鸟订单数据
    fengniao_data = {
        "partner_order_code": order.order_no,  # 使用本地订单号作为外部订单号
        "receiver_name": delivery_address['name'],
        "receiver_primary_phone": delivery_address['phone'],
        "receiver_address": delivery_address['detail'],
        "receiver_latitude": delivery_address.get('latitude', 0.0),  # 需要前端提供
        "receiver_longitude": delivery_address.get('longitude', 0.0),  # 需要前端提供
        "goods_count": len(task_info['items']),
        "goods_weight": 1.0,  # 默认重量，可以根据实际情况调整
        "goods_total_amount_cent": int(float(task_info['total_amount']) * 100),  # 转换为分
        "goods_actual_amount_cent": int(float(order.payable_amount) * 100),  # 转换为分
        "goods_item_list": goods_item_list,
        "order_type": 1,  # 1:即时单，3:预约单
        "position_source": 3,  # 3:高德地图
        "out_shop_code": settings.FENGNIAO_SHOP_ID,  # 使用配置的门店ID
    }
    
    # 如果有配送时间要求，设置为预约单
    delivery_time = task_info.get('delivery_time')
    if delivery_time and delivery_time.get('value'):
        fengniao_data["order_type"] = 3  # 预约单
        # 这里需要根据delivery_time计算expect_fetch_time和require_receive_time
        # 暂时使用当前时间+30分钟作为出餐时间，+60分钟作为送达时间
        fengniao_data["expect_fetch_time"] = current_timestamp + (30 * 60 * 1000)
        fengniao_data["require_receive_time"] = current_timestamp + (60 * 60 * 1000)
    
    return fengniao_data


def _format_delivery_address(delivery_address: dict) -> str:
    """格式化配送地址为字符串"""
    try:
        # 构建完整地址字符串
        address_parts = []

        # 添加省市区信息
        if delivery_address.get('province'):
            address_parts.append(delivery_address['province'])
        if delivery_address.get('city'):
            address_parts.append(delivery_address['city'])
        if delivery_address.get('district'):
            address_parts.append(delivery_address['district'])

        # 添加详细地址
        if delivery_address.get('detail'):
            address_parts.append(delivery_address['detail'])

        # 拼接地址
        formatted_address = ''.join(address_parts)

        # 添加收货人信息
        if delivery_address.get('name'):
            formatted_address += f" ({delivery_address['name']}"
            if delivery_address.get('phone'):
                formatted_address += f" {delivery_address['phone']}"
            formatted_address += ")"

        return formatted_address
    except Exception as e:
        logger.error(f"格式化配送地址失败: {str(e)}")
        return str(delivery_address)


def _format_delivery_time(delivery_time_data: dict) -> Optional[datetime]:
    """格式化配送时间为datetime对象"""
    try:
        if not delivery_time_data:
            return None

        # 如果有具体的时间值，尝试解析
        time_value = delivery_time_data.get('value')
        if time_value:
            # 这里需要根据实际的时间格式进行解析
            # 假设时间格式为 "12:00-15:00" 这样的时间段
            # 我们取开始时间作为配送时间
            if '-' in time_value:
                start_time = time_value.split('-')[0].strip()
                # 构建今天的这个时间
                from datetime import datetime, time as dt_time
                today = datetime.now().date()
                try:
                    hour, minute = map(int, start_time.split(':'))
                    delivery_datetime = datetime.combine(today, dt_time(hour, minute))
                    return delivery_datetime
                except ValueError:
                    logger.warning(f"无法解析时间格式: {start_time}")
                    return None

        return None
    except Exception as e:
        logger.error(f"格式化配送时间失败: {str(e)}")
        return None
