#!/usr/bin/env python3

try:
    print("Testing imports...")
    
    print("1. Testing base DAO import...")
    from app.dao.base import DAO
    print("   ✓ Base DAO imported successfully")
    
    print("2. Testing fengniao models import...")
    from app.models.fengniao import FengniaoCallback, FengniaoCallbackOrderStatus
    print("   ✓ Fengniao models imported successfully")
    
    print("3. Testing fengniao DAO import...")
    from app.dao.fengniao import <PERSON>niaoCallbackDAO, FengniaoCallbackOrderStatusDAO
    print("   ✓ Fengniao DAO imported successfully")
    
    print("4. Testing DAO instantiation...")
    dao = FengniaoCallbackDAO()
    print("   ✓ FengniaoCallbackDAO instantiated successfully")
    
    dao2 = FengniaoCallbackOrderStatusDAO()
    print("   ✓ FengniaoCallbackOrderStatusDAO instantiated successfully")
    
    print("5. Testing callback import...")
    from app.callback.fengniao import fengniao_router
    print("   ✓ Fengniao callback imported successfully")
    
    print("\n✅ All imports successful!")
    
except Exception as e:
    print(f"\n❌ Import failed: {e}")
    import traceback
    traceback.print_exc()
