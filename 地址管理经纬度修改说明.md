# 地址管理组件经纬度功能修改说明

## 修改概述

为了支持外卖订单创建时传递准确的经纬度信息，对地址管理组件和配送页面进行了以下修改：

## 修改内容

### 1. 地址管理组件 (`components/address-manager/address-manager.js`)

#### 1.1 添加经纬度字段到地址表单
- 在 `addressForm` 初始化时添加了 `latitude` 和 `longitude` 字段
- 确保新增地址和编辑地址时都包含经纬度信息

#### 1.2 修改地图选择确认方法
- 在 `confirmMapLocation()` 方法中，先保存经纬度到地址表单
- 然后调用逆地址解析获取地址文本信息

#### 1.3 修改逆地址解析方法
- 在 `reverseGeocode()` 方法中，同时保存经纬度和地址文本信息
- 确保地图选择和位置获取都能正确保存经纬度

#### 1.4 修改地址表单初始化
- `showAddAddressForm()` 方法中添加经纬度字段初始化
- `showEditAddressForm()` 方法中确保编辑时包含经纬度信息

### 2. 配送页面 (`pages/delivery/delivery.js`)

#### 2.1 修改腾讯地图API密钥
- 将占位符 `'YOUR_TENCENT_MAP_KEY'` 替换为实际的API密钥
- 使用与地址管理组件相同的密钥：`'CHMBZ-6DYKL-JW6PE-EJPKN-4POB7-VYF23'`

## 功能验证

### 测试文件
创建了测试文件用于验证功能：
- `test-address.js` - 测试页面逻辑
- `test-address.wxml` - 测试页面模板
- `test-address.wxss` - 测试页面样式

### 测试内容
1. 验证地址选择时是否正确返回经纬度信息
2. 模拟订单创建，检查订单数据中的地址是否包含经纬度
3. 显示测试结果，便于调试

## 订单创建API兼容性

修改后的地址对象现在包含以下字段，完全符合订单创建API的要求：

```json
{
  "id": "地址ID",
  "name": "收货人姓名",
  "phone": "收货人电话",
  "province": "省份",
  "city": "城市",
  "district": "区县",
  "detail": "详细地址",
  "isDefault": true/false,
  "latitude": 23.1291,    // 新增：纬度
  "longitude": 113.3185   // 新增：经度
}
```

## 使用流程

1. 用户在配送页面选择地址
2. 地址管理组件显示地址列表或地址表单
3. 用户可以通过地图选择精确位置，获取经纬度
4. 地址保存时包含经纬度信息
5. 订单创建时，`delivery_address` 包含完整的地址和经纬度信息
6. 后端可以使用经纬度信息进行配送范围判断和路径规划

## 注意事项

1. **API密钥安全性**：腾讯地图API密钥应该在生产环境中进行适当的安全配置
2. **权限处理**：确保用户已授权位置权限，否则无法获取当前位置
3. **错误处理**：网络错误或API调用失败时有适当的错误提示
4. **数据兼容性**：旧的地址数据可能没有经纬度信息，需要处理兼容性

## 后续优化建议

1. 添加地址验证，确保经纬度在合理范围内
2. 支持地址搜索功能，提高用户体验
3. 缓存地址解析结果，减少API调用次数
4. 添加地址精度指示，让用户了解定位准确性
