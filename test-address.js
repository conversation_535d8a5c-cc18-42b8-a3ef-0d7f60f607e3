// 测试地址管理组件的经纬度功能
// 这个文件用于验证地址选择时是否正确返回经纬度信息

Page({
  data: {
    selectedAddress: null,
    testResults: []
  },

  onLoad() {
    console.log('测试页面加载完成');
  },

  // 地址变化事件处理
  onAddressChange(e) {
    const selectedAddress = e.detail;
    console.log('接收到地址变化事件:', selectedAddress);
    
    // 检查地址对象是否包含经纬度
    const hasLatitude = selectedAddress && typeof selectedAddress.latitude === 'number';
    const hasLongitude = selectedAddress && typeof selectedAddress.longitude === 'number';
    
    const testResult = {
      timestamp: new Date().toLocaleString(),
      address: selectedAddress,
      hasLatitude: hasLatitude,
      hasLongitude: hasLongitude,
      isValid: hasLatitude && hasLongitude
    };
    
    this.setData({
      selectedAddress: selectedAddress,
      testResults: [...this.data.testResults, testResult]
    });
    
    // 输出测试结果
    console.log('测试结果:', testResult);
    
    if (testResult.isValid) {
      wx.showToast({
        title: '地址包含经纬度信息',
        icon: 'success'
      });
    } else {
      wx.showToast({
        title: '地址缺少经纬度信息',
        icon: 'error'
      });
    }
  },

  // 模拟订单创建
  simulateOrderCreation() {
    if (!this.data.selectedAddress) {
      wx.showToast({
        title: '请先选择地址',
        icon: 'none'
      });
      return;
    }

    const orderData = {
      order_type: 'delivery',
      items: [
        {
          dish_id: "1",
          name: "测试菜品",
          price: 22,
          quantity: 1
        }
      ],
      total_amount: 22,
      delivery_address: this.data.selectedAddress,
      delivery_time: {
        id: "1",
        label: "立即配送",
        value: "立即配送"
      },
      delivery_fee: 0
    };

    console.log('模拟订单数据:', JSON.stringify(orderData, null, 2));
    
    // 检查订单数据中的地址是否包含经纬度
    const address = orderData.delivery_address;
    if (address && typeof address.latitude === 'number' && typeof address.longitude === 'number') {
      wx.showModal({
        title: '订单数据验证成功',
        content: `地址包含经纬度信息:\n纬度: ${address.latitude}\n经度: ${address.longitude}`,
        showCancel: false
      });
    } else {
      wx.showModal({
        title: '订单数据验证失败',
        content: '地址缺少经纬度信息，无法创建订单',
        showCancel: false
      });
    }
  },

  // 清除测试结果
  clearTestResults() {
    this.setData({
      testResults: [],
      selectedAddress: null
    });
  }
});
